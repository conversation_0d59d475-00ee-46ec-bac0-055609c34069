// import { ref } from 'vue'
import type { ISearchItemType } from '@/types/lead'

export const LEAD_POOL_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择跟进状态',
        label: '跟进状态',
        options: [
            { label: '未处理', value: 1 },
            { label: '联系方式有效', value: 2 },
            { label: '联系方式无效', value: 3 },
            { label: '关闭', value: 4 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'note',
        type: 'input',
        placeholder: '请输入备注',
        label: '备注',
        editable: true,
        isShow: true,
    },
    {
        key: 'createDate',
        type: 'date',
        placeholder: '请选择创建时间',
        label: '创建时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'source',
        type: 'select',
        placeholder: '请选择数据来源',
        label: '数据来源',
        options: [
            { label: '找企业', value: 1 },
            { label: '上下游', value: 6 },
            { label: '导入', value: 10 },
            { label: '企业经营慧眼系统', value: 13 },
            { label: '新增', value: 14 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'aiphoneResult',
        type: 'select',
        placeholder: '请选择外呼结果',
        label: '外呼结果',
        options: [
            { label: '有意向', value: 1 },
            { label: '无意向', value: 0 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'tagIds',
        type: 'multipleSelect',
        placeholder: '请选择标签',
        label: '标签',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'sourceCompanyName',
        type: 'input',
        placeholder: '请输入来源企业',
        label: '来源企业',
        editable: true,
        isShow: true,
    },
    {
        key: 'channel',
        type: 'input',
        placeholder: '请输入线索渠道',
        label: '线索渠道',
        editable: true,
        isShow: true,
    },
    {
        key: 'areaCode',
        type: 'cascader',
        placeholder: '请选择区域',
        label: '区域筛选',
        options: [],
        props: {
            label: 'label',
            value: 'value',
            emitPath: false,
            checkStrictly: true,
        },
        editable: true,
        isShow: true,
    },
    {
        key: 'user',
        type: 'multipleSelect',
        placeholder: '请选择负责人',
        label: '负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'beforeUser',
        type: 'multipleSelect',
        placeholder: '请选择前负责人',
        label: '前负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'newFollowDate',
        type: 'date',
        placeholder: '请选择最新跟进时间',
        label: '最新跟进时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择创建人',
        label: '创建人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'invoiceCollectDate',
        type: 'date',
        placeholder: '请选择发票采集时间',
        label: '发票采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'taxCollectDate',
        type: 'date',
        placeholder: '请选择税务采集时间',
        label: '税务采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'basicScore',
        type: 'slider',
        placeholder: '请选择基础得分',
        label: '基础得分',
        editable: true,
        isShow: true,
    },
    // {
    //     key: 'import_batch_id',
    //     type: 'checkAllMultipleSelect',
    //     placeholder: '请选择导入项',
    //     label: '导入筛选',
    //     options: [],
    //     editable: true,
    //     isShow: true,
    // },
]
export const LEAD_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择跟进状态',
        label: '跟进状态',
        options: [
            { label: '未处理', value: 1 },
            { label: '联系方式有效', value: 2 },
            { label: '联系方式无效', value: 3 },
            { label: '关闭', value: 4 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'note',
        type: 'input',
        placeholder: '请输入备注',
        label: '备注',
        editable: true,
        isShow: true,
    },
    {
        key: 'createDate',
        type: 'date',
        placeholder: '请选择创建时间',
        label: '创建时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'source',
        type: 'select',
        placeholder: '请选择数据来源',
        label: '数据来源',
        options: [
            { label: '找企业', value: 1 },
            { label: '上下游', value: 6 },
            { label: '导入', value: 10 },
            { label: '企业经营慧眼系统', value: 13 },
            { label: '新增', value: 14 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'aiphoneResult',
        type: 'select',
        placeholder: '请选择外呼结果',
        label: '外呼结果',
        options: [
            { label: '有意向', value: 1 },
            { label: '无意向', value: 0 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'tagIds',
        type: 'multipleSelect',
        placeholder: '请选择标签',
        label: '标签',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'sourceCompanyName',
        type: 'input',
        placeholder: '请输入来源企业',
        label: '来源企业',
        editable: true,
        isShow: true,
    },
    {
        key: 'channel',
        type: 'input',
        placeholder: '请输入线索渠道',
        label: '线索渠道',
        editable: true,
        isShow: true,
    },
    {
        key: 'areaCode',
        type: 'cascader',
        placeholder: '请选择区域',
        label: '区域筛选',
        options: [],
        props: {
            label: 'label',
            value: 'value',
            emitPath: false,
            checkStrictly: true,
        },
        editable: true,
        isShow: true,
    },
    {
        key: 'user',
        type: 'multipleSelect',
        placeholder: '请选择负责人',
        label: '负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'beforeUser',
        type: 'multipleSelect',
        placeholder: '请选择前负责人',
        label: '前负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'newFollowDate',
        type: 'date',
        placeholder: '请选择最新跟进时间',
        label: '最新跟进时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择创建人',
        label: '创建人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'invoiceCollectDate',
        type: 'date',
        placeholder: '请选择发票采集时间',
        label: '发票采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'taxCollectDate',
        type: 'date',
        placeholder: '请选择税务采集时间',
        label: '税务采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'importBatchIds',
        type: 'checkAllMultipleSelect',
        placeholder: '请选择导入项',
        label: '导入筛选',
        options: [],
        editable: true,
        isShow: true,
        checkAll: false,
        isIndeterminate: false
    },
    {
        key: 'orgIds',
        type: 'multipleSelect',
        placeholder: '请选择所属组织',
        label: '所属组织',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'basicScore',
        type: 'slider',
        placeholder: '请选择基础得分',
        label: '基础得分',
        editable: true,
        isShow: true,
    },
]
export const CUSTOMER_POOL_SEARCH_OPTIONS: ISearchItemType[] = [
    // 企业名称、企业税号、跟进状态、创建时间、实际跟进时间、数据来源、标签、来源企业、客户渠道、区域筛选、创建人、负责人、前负责人、发票采集时间区间查询、税务采集时间区间查询、基础得分（0-100分查询）、最新转入时间、
    // {
    //     key: 'customerPoolName',
    //     type: 'input',
    //     placeholder: '请输入客户名称 企业名称',
    //     label: '名称 ',
    //     editable: true,  
    //     isShow: true,
    // },
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入企业税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择客户状态',
        label: '跟进状态',
        options: [
            { label: '初访', value: 1 },
            { label: '意向', value: 2 },
            { label: '报价', value: 3 },
            { label: '成交', value: 4 },
            { label: '暂时搁置', value: 5 },
            // { label: '未成交', value: 6 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'note',
        type: 'input',
        placeholder: '请输入备注',
        label: '备注',
        editable: true,
        isShow: true,
    },
    {
        key: 'createDate',
        type: 'date',
        placeholder: '请选择创建时间',
        label: '创建时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'newFollowDate',
        type: 'date',
        placeholder: '请选择最新跟进时间',
        label: '实际跟进时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'source',
        type: 'select',
        placeholder: '请选择数据来源',
        label: '数据来源',
        options: [
            { label: '找企业', value: 1 },
            { label: '上下游', value: 6 },
            { label: '导入', value: 10 },
            { label: '企业经营慧眼系统', value: 13 },
            { label: '新增', value: 14 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'tagIds',
        type: 'multipleSelect',
        placeholder: '请选择标签',
        label: '标签',
        options: [],
        editable: true,
        isShow: true
    },
    {
        key: 'sourceCompanyName',
        type: 'input',
        placeholder: '请输入来源企业',
        label: '来源企业',
        editable: true,
        isShow: true,
    },
    {
        key: 'channel',
        type: 'input',
        placeholder: '请输入客户渠道',
        label: '客户渠道',
        editable: true,
        isShow: true,
    },
    {
        key: 'areaCode',
        type: 'cascader',
        placeholder: '请选择地址',
        label: '区域筛选',
        options: [],
        props: {
            label: 'label',
            value: 'value',
            emitPath: false,
            checkStrictly: true,
        },
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择创建人',
        label: '创建人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'user',
        type: 'multipleSelect',
        placeholder: '请选择负责人',
        label: '负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'beforeUser',
        type: 'multipleSelect',
        placeholder: '请选择前负责人',
        label: '前负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'invoiceCollectDate',
        type: 'date',
        placeholder: '请选择发票采集时间',
        label: '发票采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'taxCollectDate',
        type: 'date',
        placeholder: '请选择税务采集时间',
        label: '税务采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'basicScore',
        type: 'slider',
        placeholder: '请选择基础得分',
        label: '基础得分',
        editable: true,
        isShow: true,
    },
    {
        key: 'turnCustomerPoolDate',
        type: 'date',
        placeholder: '请选择转入客户公海时间',
        label: '最新转入时间',
        editable: true,
        isShow: true,
    },
]
export const CUSTOMER_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择跟进状态',
        label: '跟进状态',
        options: [
            { label: '初访', value: 1 },
            { label: '意向', value: 2 },
            { label: '报价', value: 3 },
            { label: '成交', value: 4 },
            { label: '暂时搁置', value: 5 },
            // { label: '未成交', value: 6 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'note',
        type: 'input',
        placeholder: '请输入备注',
        label: '备注',
        editable: true,
        isShow: true,
    },
    {
        key: 'createDate',
        type: 'date',
        placeholder: '请选择创建时间',
        label: '创建时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'newFollowDate',
        type: 'date',
        placeholder: '请选择最新跟进时间',
        label: '实际跟进时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'source',
        type: 'select',
        placeholder: '请选择数据来源',
        label: '数据来源',
        options: [
            { label: '找企业', value: 1 },
            { label: '上下游', value: 6 },
            { label: '导入', value: 10 },
            { label: '企业经营慧眼系统', value: 13 },
            { label: '新增', value: 14 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'tagIds',
        type: 'multipleSelect',
        placeholder: '请选择标签',
        label: '标签',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'sourceCompanyName',
        type: 'input',
        placeholder: '请输入来源企业',
        label: '来源企业',
        editable: true,
        isShow: true,
    },
    {
        key: 'channel',
        type: 'input',
        placeholder: '请输入客户渠道',
        label: '客户渠道',
        editable: true,
        isShow: true,
    },
    {
        key: 'areaCode',
        type: 'cascader',
        placeholder: '请选择区域',
        label: '区域筛选',
        options: [],
        props: {
            label: 'label',
            value: 'value',
            emitPath: false,
            checkStrictly: true,
        },
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择创建人',
        label: '创建人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'ministrantInfo',
        type: 'checkAllMultipleSelect',
        placeholder: '请选择协作人',
        label: '协作人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'user',
        type: 'multipleSelect',
        placeholder: '请选择负责人',
        label: '负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'beforeUser',
        type: 'multipleSelect',
        placeholder: '请选择前负责人',
        label: '前负责人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'invoiceCollectDate',
        type: 'date',
        placeholder: '请选择发票采集时间',
        label: '发票采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'taxCollectDate',
        type: 'date',
        placeholder: '请选择税务采集时间',
        label: '税务采集',
        editable: true,
        isShow: true,
    },
    {
        key: 'importBatchIds',
        type: 'checkAllMultipleSelect',
        placeholder: '请选择导入项',
        label: '导入筛选',
        options: [],
        editable: true,
        isShow: true,
        checkAll: false,
        isIndeterminate: false
    },
    {
        key: 'turnCustomerDate',
        type: 'date',
        placeholder: '最新转入时间',
        label: '最新转入时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'orgIds',
        type: 'multipleSelect',
        placeholder: '请选择所属组织',
        label: '所属组织',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'basicScore',
        type: 'slider',
        placeholder: '请选择基础得分',
        label: '基础得分',
        editable: true,
        isShow: true,
    },
]
export const TENANT_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'name',
        type: 'input',
        placeholder: '请输入租户名称',
        label: '租户名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择状态',
        label: '状态',
        options: [
            { label: '已启用', value: 1 },
            { label: '已停用', value: 0 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'type',
        type: 'select',
        placeholder: '请选择类型',
        label: '类型',
        options: [
            { label: 'B端', value: 1 },
            { label: 'C端', value: 2 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'contact',
        type: 'input',
        placeholder: '请输入联系人',
        label: '联系人',
        editable: true,
        isShow: true,
    },
    {
        key: 'phone',
        type: 'input',
        placeholder: '请输入联系电话',
        label: '联系电话',
        editable: true,
        isShow: true,
    },
    {
        key: 'username',
        type: 'input',
        placeholder: '请输入用户名',
        label: '用户名',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'input',
        placeholder: '请输入创建人',
        label: '创建人',
        editable: true,
        isShow: true,
    },
]

export const TENANT_SEARCH_OPTIONS_FORADMIN: ISearchItemType[] = [
    {
        key: 'name',
        type: 'input',
        placeholder: '请输入租户名称',
        label: '租户名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择状态',
        label: '状态',
        options: [
            { label: '已启用', value: 1 },
            { label: '已停用', value: 0 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'type',
        type: 'select',
        placeholder: '请选择类型',
        label: '类型',
        options: [
            { label: 'B端', value: 1 },
            { label: 'C端', value: 2 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'contact',
        type: 'input',
        placeholder: '请输入联系人',
        label: '联系人',
        editable: true,
        isShow: true,
    },
    {
        key: 'phone',
        type: 'input',
        placeholder: '请输入联系电话',
        label: '联系电话',
        editable: true,
        isShow: true,
    },
    {
        key: 'username',
        type: 'input',
        placeholder: '请输入用户名',
        label: '用户名',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'input',
        placeholder: '请输入创建人',
        label: '创建人',
        editable: true,
        isShow: true,
    },
    {
        key: 'isOEM',
        type: 'select',
        placeholder: '是否为OEM',
        label: '是否为OEM',
        options: [
            { label: '是', value: true },
            { label: '否', value: false },
        ],
        editable: true,
        isShow: true,
    },
]

export const COLLECT_SEARCH_OPTIONS_FORYUANQUADMIN: ISearchItemType[] = [
    {
        key: 'orgIds',
        type: 'multipleSelect',
        placeholder: '请选择组织',
        label: '组织名称',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'multipleSelect',
        placeholder: '请选择发起人',
        label: '发起人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'collectType',
        type: 'select',
        placeholder: '请选择采集类型',
        label: '采集类型',
        options: [
            { label: '发票采集', value: 'INVOICE' },
            { label: '财税采集', value: 'TAX' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择采集状态',
        label: '采集状态',
        options: [
            { label: '采集完成', value: 'SUCCESS' },
            { label: '采集中', value: 'COLLECTING' },
            { label: '等待采集', value: 'WAITING' },
            { label: '采集失败', value: 'ERROR' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入企业税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    }
]
export const COLLECT_SEARCH_OPTIONS_FORADMIN: ISearchItemType[] = [
    {
        key: 'orgIds',
        type: 'multipleSelect',
        placeholder: '请选择组织',
        label: '组织名称',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'multipleSelect',
        placeholder: '请选择发起人',
        label: '发起人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'collectType',
        type: 'select',
        placeholder: '请选择采集类型',
        label: '采集类型',
        options: [
            { label: '发票采集', value: 'INVOICE' },
            { label: '财税采集', value: 'TAX' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择采集状态',
        label: '采集状态',
        options: [
            { label: '采集完成', value: 'SUCCESS' },
            { label: '采集中', value: 'COLLECTING' },
            { label: '等待采集', value: 'WAITING' },
            { label: '采集失败', value: 'ERROR' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入企业税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'tenantId',
        type: 'multipleSelect',
        placeholder: '请选择所属租户',
        label: '所属租户',
        options: [],
        editable: true,
        isShow: true,
    },
]
export const COLLECT_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'createUser',
        type: 'multipleSelect',
        placeholder: '请选择发起人',
        label: '发起人',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'collectType',
        type: 'select',
        placeholder: '请选择采集类型',
        label: '采集类型',
        options: [
            { label: '发票采集', value: 'INVOICE' },
            { label: '财税采集', value: 'TAX' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择采集状态',
        label: '采集状态',
        options: [
            { label: '采集完成', value: 'SUCCESS' },
            { label: '采集中', value: 'COLLECTING' },
            { label: '等待采集', value: 'WAITING' },
            { label: '采集失败', value: 'ERROR' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'companyName',
        type: 'input',
        placeholder: '请输入企业名称',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'socialCreditCode',
        type: 'input',
        placeholder: '请输入企业税号',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
]
export const DATA_CHANGE_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'name',
        type: 'input',
        placeholder: '请输入租户名称',
        label: '租户名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择状态',
        label: '状态',
        options: [
            { label: '已启用', value: 1 },
            { label: '已停用', value: 0 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'type',
        type: 'select',
        placeholder: '请选择类型',
        label: '类型',
        options: [
            { label: 'B端', value: 1 },
            { label: 'C端', value: 2 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'contact',
        type: 'input',
        placeholder: '请输入联系人',
        label: '联系人',
        editable: true,
        isShow: true,
    },
    {
        key: 'phone',
        type: 'input',
        placeholder: '请输入联系电话',
        label: '联系电话',
        editable: true,
        isShow: true,
    },
    {
        key: 'username',
        type: 'input',
        placeholder: '请输入用户名',
        label: '用户名',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'input',
        placeholder: '请输入创建人',
        label: '创建人',
        editable: true,
        isShow: true,
    },
]
export const NOTIFICATION_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'createDate',
        type: 'date',
        placeholder: '请选择消息时间',
        label: '消息时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'isRead',
        type: 'select',
        placeholder: '请选择消息状态',
        label: '消息状态',
        options: [
            { label: '已读', value: true },
            { label: '未读', value: false},
        ],
        editable: true,
        isShow: true,
    },
    // {
    //     key: 'msgType',
    //     type: 'select',
    //     placeholder: '请选择消息类型',
    //     label: '消息类型',
    //     options: [
    //         { label: '线索/线索池', value: 1 },
    //         { label: '客户/客户公海', value: 2 },
    //     ],
    //     editable: true,
    //     isShow: true,
    // },
]
export const ADVANCE_TEMPLETE_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'templateName',
        type: 'input',
        placeholder: '请输入模板名称',
        label: '模板名称',
        editable: true,
        isShow: true,
    },
]

export const POLICY_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'policyName',
        type: 'input',
        label: '政策名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'policyLevel',
        type: 'select',
        label: '政策级别',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'issuingDepartment',
        type: 'select',
        label: '发文部门',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'policyTopic',
        type: 'select',
        label: '主题分类',
        editable: true,
        isShow: true,
    },
    {
        key: 'issuingTime',
        type: 'date',
        label: '发文时间',
        editable: true,
        isShow: true,
    }
]

export const TAG_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'tagName',
        type: 'input',
        label: '标签名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'createTimes',
        type: 'date',
        label: '创建时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择创建人',
        label: '创建人',
        options: [],
        editable: true,
        isShow: true,
    },
]

export const TASK_MANAGEMENT: ISearchItemType[] = [
    {
        key: 'taskName',
        type: 'input',
        label: '任务名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'taskType',
        type: 'select',
        placeholder: '请选择任务类型',
        label: '类型',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择任务状态',
        label: '状态',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'createTimes',
        type: 'date',
        label: '创建时间',
        editable: true,
        isShow: true,
    },
]

export const FINANCE_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'financialName',
        type: 'input',
        label: '产品名称',
        editable: true,
        isShow: true,
    }
]

export const BENEFIT_MANAMENT_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'tenantId',
        type: 'multipleSelect',
        placeholder: '请选择所属租户',
        label: '所属租户',
        options: [],
        editable: true,
        isShow: true,
    },
]

export const BENEFIT_LIST_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'serviceKeys',
        type: 'select',
        placeholder: '请选择权益类型',
        label: '权益名称',
        options: [
            { label: '线索联系方式', value: 'xs' },
            { label: '高新技术科技企业报告', value: 'gqbg' },
            { label: '企业发展数据综合报告分析', value: 'fpbg' },
            { label: '企业财税经营分析报告', value: 'swbg' },
            { label: '智能外呼', value: 'znwh' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择权益状态',
        label: '状态',
        options: [
            { label: '有效', value: 1 },
            { label: '过期', value: 2 },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'createTimes',
        type: 'date',
        label: '购买时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'expiredTimes',
        type: 'date',
        label: '到期时间',
        editable: true,
        isShow: true,
    },
]
export const BENEFIT_LIST_SEARCH_OPTIONS_FORADMIN: ISearchItemType[] = [
    {
        key: 'serviceKeys',
        type: 'select',
        placeholder: '请选择权益类型',
        label: '权益名称',
        options: [
            { label: '线索联系方式', value: 'xs' },
            { label: '高新技术科技企业报告', value: 'gqbg' },
            { label: '企业发展数据综合报告分析', value: 'fpbg' },
            { label: '企业财税经营分析报告', value: 'swbg' },
            { label: '智能外呼', value: 'znwh' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择权益状态',
        label: '状态',
        options: [
            { label: '有效', value: '1' },
            { label: '过期', value: '2' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'createTimes',
        type: 'date',
        label: '购买时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'expiredTimes',
        type: 'date',
        label: '到期时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'tenantId',
        type: 'multipleSelect',
        placeholder: '请选择所属租户',
        label: '所属租户',
        options: [],
        editable: true,
        isShow: true,
    },
]

export const BENEFIT_RECORD_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'serviceKeys',
        type: 'select',
        placeholder: '请选择权益类型',
        label: '权益类型',
        options: [
            { label: '线索联系方式', value: 'xs' },
            { label: '高新技术科技企业报告', value: 'gqbg' },
            { label: '企业发展数据综合报告分析', value: 'fpbg' },
            { label: '企业财税经营分析报告', value: 'swbg' },
            { label: '智能外呼', value: 'znwh' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'type',
        type: 'select',
        placeholder: '请选择类型',
        label: '类型',
        options: [
            { label: '正常', value: '1' },
            { label: '过期', value: '2' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'createTimes',
        type: 'date',
        label: '消费时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'companyName',
        type: 'input',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'nsrsbh',
        type: 'input',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择操作员',
        label: '操作员',
        options: [],
        editable: true,
        isShow: true,
    },
]

export const BENEFIT_RECORD_SEARCH_OPTIONS_FORADMIN: ISearchItemType[] = [
    {
        key: 'serviceKeys',
        type: 'select',
        placeholder: '请选择权益类型',
        label: '权益类型',
        options: [
            { label: '线索联系方式', value: 'xs' },
            { label: '高新技术科技企业报告', value: 'gqbg' },
            { label: '企业发展数据综合报告分析', value: 'fpbg' },
            { label: '企业财税经营分析报告', value: 'swbg' },
            { label: '智能外呼', value: 'znwh' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'type',
        type: 'select',
        placeholder: '请选择类型',
        label: '类型',
        options: [
            { label: '正常', value: '1' },
            { label: '过期', value: '2' },
        ],
        editable: true,
        isShow: true,
    },
    {
        key: 'createTimes',
        type: 'date',
        label: '消费时间',
        editable: true,
        isShow: true,
    },
    {
        key: 'companyName',
        type: 'input',
        label: '企业名称',
        editable: true,
        isShow: true,
    },
    {
        key: 'nsrsbh',
        type: 'input',
        label: '企业税号',
        editable: true,
        isShow: true,
    },
    {
        key: 'createUser',
        type: 'select',
        placeholder: '请选择操作员',
        label: '操作员',
        options: [],
        editable: true,
        isShow: true,
    },
    {
        key: 'tenantId',
        type: 'multipleSelect',
        placeholder: '请选择所属租户',
        label: '所属租户',
        options: [],
        editable: true,
        isShow: true,
    },
]

export const OEM_MANAGEMENT_SEARCH_OPTIONS: ISearchItemType[] = [
    {
        key: 'oem-key',
        type: 'input',
        label: 'oem-key',
        placeholder: '请输入oem-key',
        editable: true,
        isShow: true,
    },
    {
        key: 'companyName',
        type: 'input',
        label: '底部公司名',
        placeholder: '请输入公司名',
        editable: true,
        isShow: true,
    },
]
