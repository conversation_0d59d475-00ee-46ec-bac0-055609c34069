<template>
    <el-table
        :ref="tableRef"
        :data="tableData"
        height="500"
        @selection-change="selectionChange"
        show-overflow-tooltip
        row-key="id"
        :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
    >
        <template #empty>
            <div class="display-flex flex-column top-bottom-center">
                <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                <div class="font-first-title-unactive color-two-grey">暂无数据</div>
            </div>
        </template>
        <el-table-column type="selection" width="64" reserve-selection></el-table-column>
        <el-table-column label="企业信息">
            <template #default="scope">
                <div class="t-margin-6 display-flex font-18 pointer" @click="ClickCompanyName(scope.row)">{{ scope.row.companyName }}</div>
                <div class="display-flex flex-wrap" >
                    <div class="l-margin-4 t-margin-4" v-for="(tagItem,i) in scope.row.companyTags" :key="i">
                        <el-tag class="font-14" effect="plain" :type="getTagClass(tagItem.categoryCode)">{{ tagItem.tagName }}</el-tag>
                    </div>
                </div>
                <div class="t-margin-6 display-flex font-16">
                    <div>{{ scope.row.legalperson }}</div>
                    <div class="r-margin-6 l-margin-6">|</div>
                    <div>{{ scope.row.esdate }}</div>
                    <div class="r-margin-6 l-margin-6">|</div>
                    <div>{{ scope.row.registercapital }}{{ scope.row.regcapcur }}</div>
                </div>
                <div class="t-margin-6 display-flex color-three-grey font-16">地区：{{ scope.row.companyArea }}</div>
                <div class="t-margin-6 display-flex color-three-grey font-16">招标投数：{{ scope.row.tenderCount }}</div>
            </template>
        </el-table-column>
        <el-table-column label="最新公告" style="width: 40%">
            <template #default="scope">
                <div class="t-margin-10 display-flex font-16">
                    <span class="color-three-grey" style="min-width: 70px;">公告类型:</span>
                    {{ scope.row.tenderType ? scope.row.tenderType : '-' }}
                </div>
                <div class="t-margin-6 display-flex font-16 color-three-grey">
                    <div>{{ moment(scope.row.tenderPublicDate).format('YYYY-MM-DD') }}</div>
                    <div class="r-margin-6 l-margin-6">|</div>
                    <div>{{ scope.row.tenderArea ? scope.row.tenderArea : '-' }}</div>
                </div>
                <div class="t-margin-10 display-flex font-16">
                    <span class="color-three-grey">标的物:</span>
                    {{ scope.row.announcementSubject ? scope.row.announcementSubject : '-' }}
                </div>
                <div class="t-margin-10 font-16 display-flex" style="white-space: normal;">
                    <span class="color-three-grey" style="min-width: 70px;">项目名称:</span> 
                    <div>
                        <span>
                            {{ scope.row.projectName }}
                        </span>
                    </div>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <CompanyDetaiDrawer 
        v-if="companyDetailDrawerVisible" 
        v-model:drawer="companyDetailDrawerVisible" 
        :socialCreditCode="currentCompany.socialCreditCode"
        @refreshList="props.refreshCompanyList"
    ></CompanyDetaiDrawer>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref, } from 'vue'
import type { Ref } from 'vue'
import type {ISearchBidCompanyItem, ICompanyInfo} from '@/types/company'
import {ElMessage} from 'element-plus'
import CompanyDetaiDrawer from '@/components/search/company-detail-drawer/company-detail-drawer.vue'

const tableRef = ref()
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const companyDetailDrawerVisible = ref<boolean>(false)
const props = defineProps<{
    tableData: ISearchBidCompanyItem[]
    refreshCompanyList: () => void
}>()

const emit = defineEmits(['selection-change'])

const checkIds = ref<string[]>([])
const selectionChange = ( val:ICompanyInfo[] ) => {
    checkIds.value = val.map((i) => {
        return i.id
    })
    emit('selection-change', val)
    // console.log('选中的行',val)
}

const currentCompany : Ref<ISearchBidCompanyItem> = ref({} as ISearchBidCompanyItem)
const ClickCompanyName = (row: ISearchBidCompanyItem) => {
    if (!row.socialCreditCode) {
        ElMessage.error('该企业暂无详情')
        return
    }
    console.log('点击企业',row)
    currentCompany.value = row
    companyDetailDrawerVisible.value = true
}

const getTagClass = (code: string) => {
    switch(code){
    case '001':
        return 'success'
    case '002':
        return 'info'
    case '003':
        return 'success'
    case '004':
        return 'info'
    case '005':
        return 'primary'
    case '006':
        return 'danger'
    case '007':
        return 'warning'
    default:
        return 'danger'
    }
}

onMounted(() => {
    console.log('props',props.tableData)    
})
</script>

<style scoped>

</style>