<template>
    <div style="background-color: #f7f7f7;">
        <div style="margin-bottom: 16px; background-color: #fff; padding: 16px">
            <searchBox
                :searchOptionKey="'OEM_MANAGEMENT_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
            >
            </searchBox>
        </div>
        <div style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="display-flex justify-flex-end font-16 b-margin-16">
                <!-- 新增按钮 -->
                <el-button type="primary" @click="handleAdd">新增</el-button>
            </div>
            <el-table
                :data="tagList"
                style="width: 100%;height: 500px;"
                empty-text="暂无数据"
                v-loading="tableLoading"
                show-overflow-tooltip
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="oem-key" prop="oem_key" min-width="150"></el-table-column>
                <el-table-column label="底部时间" prop="login_Time" width="160"></el-table-column>
                <el-table-column label="底部公司名" prop="login_CompanyName" width="170"></el-table-column>
                <el-table-column label="左侧顶部文字" prop="login_Text" width="170"></el-table-column>
                <el-table-column label="左上角logo" prop="login_Logo" width="160"></el-table-column>
                <el-table-column label="登录页图片" prop="loginPic" width="150"></el-table-column>
                <el-table-column label="网页网签标题" prop="htmlTitle" width="150"></el-table-column>
                <el-table-column label="底部备案号" prop="bottomICP" width="150"></el-table-column>
                <el-table-column label="首页上方的logo" prop="homeUpLogo" width="150"></el-table-column>
                <el-table-column label="帮助手册地址" prop="helpUrl" width="150"></el-table-column>
                <el-table-column label="首页logo" prop="H5HomeLogo" width="150"></el-table-column>
                <el-table-column label="首页顶部图" prop="H5HomeTopPic" width="150"></el-table-column>
                <el-table-column label="首页搜索下方图" prop="H5BottomPic" width="150"></el-table-column>
                <el-table-column label="是否开启精简授权" prop="isSimplifyAuth" width="160"></el-table-column>
                <el-table-column label="是否开启票易融" prop="isPYR" width="150"></el-table-column>
                <el-table-column label="业务进件二维码海报图" prop="BusinessEntryPostPic" width="200"></el-table-column>
                <el-table-column label="业务进件分享图" prop="BusinessEntrySharePic" width="150"></el-table-column>
                <el-table-column label="业务进件二维码信息" prop="BusinessEntryEWMInfo" width="180"></el-table-column>
                <el-table-column label="高企报告封面" prop="GQBGCover" width="150"></el-table-column>
                <el-table-column label="税务报告封面" prop="SWBGCover" width="150"></el-table-column>
                <el-table-column label="发票报告封面" prop="FPBGCover" width="150"></el-table-column>
                <el-table-column label="数据大屏标题" prop="ScreenTitle" width="150"></el-table-column>
                <el-table-column label="授权协议" prop="Authorization" width="150"></el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="150px"
                >
                    <template #default="scope" >
                        <div class="display-flex gap-10">
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="editOEM(scope.row)"
                            >
                                编辑
                            </div>
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="deleteOEM(scope.row)"
                            >
                                删除
                            </div>
                        </div>

                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="60%"
        @close="dialogVisible = false"
    >   
        <AddOEM v-if="addDialogVisible" @cancel="handleCancel" ></AddOEM>
        <EditOEM v-if="editDialogVisible" :addData="currentRow" @cancel="handleCancel"></EditOEM>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import AddOEM from './components/addOEM.vue'
import EditOEM from './components/editOEM.vue'

const updateSearchParams = () => {

}

const handleAdd = () => {
    dialogTitle.value = '新增'
    addDialogVisible.value = true
    editDialogVisible.value = false
    dialogVisible.value = true
}

const editOEM = (row:OEMListItem) => {
    console.log(row)
    dialogTitle.value = '编辑'
    addDialogVisible.value = false
    editDialogVisible.value = true
    dialogVisible.value = true
}

const deleteOEM = (row:OEMListItem) => {
    console.log(row)
    ElMessageBox.confirm('是否删除该企业OEM配置', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        // deleteRow(row)
    })
}

type OEMListItem = {
    id:string
    oem_key:string
    login_Time?:string
    login_CompanyName?:string
    login_Text?:string
    login_Logo?:string
    loginPic?:string
    htmlTitle?:string
    bottomICP?:string
    homeUpLogo?:string
    helpUrl?:string
    H5HomeLogo?:string
    H5HomeTopPic?:string
    H5BottomPic?:string
    isSimplifyAuth?:string
    isPYR?:string
    BusinessEntryPostPic?:string
    BusinessEntrySharePic?:string
    BusinessEntryEWMInfo?:string
    GQBGCover?:string
    SWBGCover?:string
    FPBGCover?:string
    ScreenTitle?:string
    Authorization?:string
}

const tableLoading = ref(false)
const tagList = ref<OEMListItem[]>([
    {
        id: '1',
        oem_key: 'lufeiceshi',
        login_Time: '2022-01-01',
        login_CompanyName:'我时候ih法师浮沙符合阿速发货啊',
        login_Text:'我时候ih法师浮沙符合阿速发货啊',
        login_Logo:'',
    }
])

const dialogTitle = ref('')
const dialogVisible = ref(false)
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentRow = ref<OEMListItem>()
const handleCancel = () => {
    dialogVisible.value = false
}

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

</style>