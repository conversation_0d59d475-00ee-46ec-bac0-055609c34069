<script setup lang="ts">
import type { IAddMenuForm, IMenuAddRequest, IMenuResponse } from '@/types/menu'
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import systemService from '@/service/systemService'
import { flatten } from '@/utils/flatten'

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance | null>(null)

const props = defineProps<{
    visible: boolean
    onClose: (v: { refresh?: boolean; closed?: boolean }) => void
    type: string
    currentMenu: IMenuResponse | null
    source: 0 | 1 | 2 | 3
}>()

const menuListRef = ref<IMenuResponse[]>([])

const origin = Object.freeze({
    icon: '',
    id: '',
    menuId: '',
    name: '',
    parentId: '',
    sort: 1,
    type: undefined,
    url: '',
    status: 1,
})

const form = ref<{
    icon: string
    id: string
    menuId: string
    name: string
    parentId: string
    sort: number
    type: number | undefined
    url: string
    status: number
}>(JSON.parse(JSON.stringify(origin)))

const rules = reactive<FormRules<typeof form>>({
    name: [{ required: true, message: '请输入菜单/权限名称', trigger: 'change' }],
    type: [{ required: true, message: '请选择新增类型', trigger: 'change' }],
    menuId: [{ required: true, message: '请输入菜单/权限标识', trigger: 'change' }],
    parentId: [{ required: true, message: '请选择上级菜单', trigger: 'change' }],
})

const handleClose = (refresh?: boolean) => {
    dialogVisible.value = false
    props.onClose({ refresh })
}

const save = () => {
    if (props.type === 'add') {
        add()
    }

    if (props.type === 'edit') {
        edit()
    }
}

// computed

const menuList = computed(() => {
    if (form.value.type === 3) {
        const flattenMenu = flatten(menuListRef.value)
        // const filterMenu = flattenMenu.filter((item) => item.type === 2)
        return flattenMenu
    }
    return menuListRef.value
})

const dialogTitle = computed(() => {
    return props.type === 'add' ? '新增菜单/权限' : '编辑菜单/权限'
})

const isAddMenu = computed(() => {
    return props.type === 'add' && !props.currentMenu
})

const isFirstAdd = computed(() => {
    return props.type === 'add' && props.currentMenu?.type === 1
})

// 新增子菜单
const isAddSubMenu = computed(() => {
    return props.type === 'add' && props.currentMenu?.type === 1
})

// 新增权限
const isAddPermission = computed(() => {
    return props.type === 'add' && (props.currentMenu?.type === 2 || props.currentMenu?.type === 1)
})

const isEditMenu = computed(() => {
    return props.type === 'edit' && props.currentMenu?.type === 1
})

// 新增子菜单
const isEditSubMenu = computed(() => {
    return props.type === 'edit' && props.currentMenu?.type === 2
})

// 新增权限
const isEditPermission = computed(() => {
    return props.type === 'edit' && props.currentMenu?.type === 3
})

const add = async () => {
    if (loading.value) return
    if (!formRef.value) return

    await formRef.value.validate((valid) => {
        if (valid) {
            const parmas = checkParams()
            if (!parmas) return

            loading.value = true
            systemService
                .menuAdd(parmas)
                .then((res) => {
                    loading.value = false
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '添加失败')
                    } else {
                        ElMessage.success('添加成功')
                        handleClose(true)
                        resetForm()
                    }
                })
                .catch(() => {
                    loading.value = false
                    ElMessage.error('添加失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const edit = async () => {
    if (loading.value) return
    if (!formRef.value) return
    if (!props.currentMenu) return

    await formRef.value.validate((valid) => {
        if (valid) {
            const parmas = checkParams()
            if (!parmas) return

            loading.value = true
            systemService
                .menuEdit({ ...parmas, id: props.currentMenu?.id || '' })
                .then((res) => {
                    loading.value = true
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '操作失败')
                    } else {
                        ElMessage.success('编辑成功')
                        handleClose(true)
                        resetForm()
                    }
                })
                .catch(() => {
                    loading.value = true
                    ElMessage.error('操作失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const checkParams = () => {
    const temp: IAddMenuForm = JSON.parse(JSON.stringify(form.value))

    if (temp.menuId === '') {
        ElMessage.error('请填写标识')
        return false
    }

    if (temp.type === 1) {
        temp.parentId = ''
    }

    if (!temp.type) {
        ElMessage.error('请选择类型')
        return false
    }

    if (temp.type === 2 && !temp.url) {
        ElMessage.error('请填写子菜单URL')
        return false
    }

    const params: IMenuAddRequest = { ...temp, type: temp.type }

    return params
}

const resetForm = () => {
    form.value = origin
    if (!formRef.value) return
    formRef.value.resetFields()
}

const getList = () => {
    systemService.menuTree().then((res) => {
        const { errCode, data } = res
        if (errCode === 0) {
            menuListRef.value = data
        } else {
            menuListRef.value = []
        }
    })
}

const handleMenuInfo = () => {
    if (!props.currentMenu) return
    if (props.type === 'add') {
        console.log(props.currentMenu)
        form.value.parentId = props.currentMenu.menuId
        form.value.type = props.currentMenu.type + 1
    }

    if (props.type === 'edit') {
        form.value.parentId = props.currentMenu.parentId
        form.value.type = props.currentMenu.type
        form.value.menuId = props.currentMenu.menuId
        form.value.name = props.currentMenu.name
        form.value.sort = props.currentMenu.sort
        form.value.url = props.currentMenu.url
        form.value.icon = props.currentMenu.icon
        form.value.status = props.currentMenu.status
    }
}

const onFormTypeChange = () => {
    form.value.parentId = ''
}

watch(
    () => props.visible,
    (val) => {
        dialogVisible.value = val
        if (val) {
            getList()
            handleMenuInfo()
        } else {
            resetForm()
        }
    }
)
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @close="handleClose()"
        @closed="onClose({ closed: true })"
    >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="90" label-position="left">
            <el-form-item label="类型" label-position="left" prop="type">
                <el-radio-group v-model="form.type" :disabled="!isAddMenu && !isFirstAdd" @change="onFormTypeChange">
                    <el-radio :value="1" size="large" v-if="isAddMenu || isEditMenu">一级菜单</el-radio>
                    <el-radio :value="2" size="large" v-if="isAddMenu || isAddSubMenu || isEditSubMenu">
                        子菜单
                    </el-radio>
                    <el-radio :value="3" size="large" v-if="isAddMenu || isAddPermission || isEditPermission">
                        权限
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="名称" label-position="left" prop="name">
                <el-input v-model="form.name" clearable />
            </el-form-item>
            <el-form-item label="标识" label-position="left" prop="menuId">
                <el-input v-model="form.menuId" clearable />
            </el-form-item>
            <el-form-item label="URL" label-position="left" prop="url">
                <el-input v-model="form.url" clearable />
            </el-form-item>

            <el-form-item label="图标" label-position="left" prop="icon">
                <el-input v-model="form.icon" clearable />
            </el-form-item>
            <el-form-item label="排序" label-position="left" prop="sort">
                <el-input v-model="form.sort" clearable />
            </el-form-item>
            <el-form-item label="父级菜单" label-position="left" prop="parentId" v-if="form.type !== 1">
                <el-select v-model="form.parentId" placeholder="请选择父级菜单" clearable>
                    <el-option v-for="menu in menuList" :key="menu.id" :label="menu.name" :value="menu.menuId" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否启用" label-position="left" prop="status">
                <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose()">取消</el-button>
                <el-button type="primary" @click="save()" :loading="loading"> 提交 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped></style>
