<template>
    <div class="display-flex justify-flex-end b-margin-10 absolute" style="right: 0; top: 0; z-index: 2">
        <el-button class="back-color-blue" type="primary" @click="addCustomContact">新增</el-button>
    </div>
    <el-table
        ref="table"
        :data="contactsData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无联系方式"
    >
        <el-table-column prop="contact" label="名称" align="left" />
        <el-table-column prop="job" label="职务" align="left" />
        <el-table-column prop="content" label="联系方式" align="left" />
        <el-table-column prop="note" label="备注" align="left" />
        <el-table-column prop="note" label="操作" align="left">
            <template #default="scope">
                <el-button text type="primary" @click="editCustomContact(scope.row)">编辑</el-button>
                <el-button text type="danger" @click="delCustomContact(scope.$index)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>
    <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
        <el-pagination
            v-model:currentPage="pageInfo.page"
            v-model:page-size="pageInfo.pageSize"
            layout="total, prev, pager, next"
            :total="pageInfo.total"
        />
    </div>

    <el-dialog v-model="addCustomContactFlag" :title="(editDialogFlag ? '编辑' : '新增') + '联系人'" width="500">
        <el-form
            ref="ruleFormRef"
            :model="addCustomContactData"
            label-width="auto"
            :rules="rules"
            :label-position="'top'"
        >
            <el-form-item label="名称" prop="contact" required>
                <el-input v-model="addCustomContactData.contact" />
            </el-form-item>
            <el-form-item label="职务" prop="job">
                <el-input v-model="addCustomContactData.job" />
            </el-form-item>
            <el-form-item label="联系方式" prop="content" required>
                <el-input v-model="addCustomContactData.content" />
            </el-form-item>
            <el-form-item label="备注" prop="note">
                <el-input v-model="addCustomContactData.note" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="addCustomContactFlag = false">取消</el-button>
                <el-button type="primary" @click="submitFrom(ruleFormRef)"> 提交 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, inject, reactive } from 'vue'
import type { Ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ILeadData } from '@/types/lead'
import crmService from '@/service/crmService'
const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const ruleFormRef = ref<FormInstance>()

const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: crmDetail.value.supplementContacts?.length || 0,
})

interface IAddCustomContactData {
    contact: string
    content: string
    note: string
    job: string
    index?: number
}

const addCustomContactData: Ref<IAddCustomContactData> = ref({
    contact: '',
    content: '',
    note: '',
    job: '',
})

const addCustomContactFlag: Ref<boolean> = ref(false)

const rules = reactive<FormRules<IAddCustomContactData>>({
    contact: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' },
    ],
    job: [{ min: 0, max: 30, message: '长度在 0 到 30 个字符', trigger: 'blur' }],
    content: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
    note: [{ min: 0, max: 50, message: '长度在 0 到 50 个字符', trigger: 'blur' }],
})

const editIndex: Ref<number> = ref(-1)

const submitFrom = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid) => {
        if (valid) {
            let supplementContacts = crmDetail.value.supplementContacts
                ? JSON.parse(JSON.stringify(crmDetail.value.supplementContacts))
                : []

            if (editIndex.value > -1) {
                supplementContacts[editIndex.value] = addCustomContactData.value
            } else {
                supplementContacts.push(addCustomContactData.value)
            }

            crmService.crmUpdate({ supplementContacts, leadId: crmDetail.value.id }).then(() => {
                crmDetail.value.supplementContacts = supplementContacts
                ElMessage({
                    type: 'success',
                    message: '操作成功',
                })
                addCustomContactFlag.value = false

                if (editIndex.value === -1) {
                    pageInfo.value.total += 1
                }
            })
        } else {
            console.log('校验失败')
        }
    })
}

const editDialogFlag: Ref<boolean> = ref(false)
const addCustomContact = () => {
    addCustomContactData.value = {
        contact: '',
        content: '',
        note: '',
        job: '',
    }
    editIndex.value = -1
    editDialogFlag.value = false
    addCustomContactFlag.value = true
}

const editCustomContact = (row: IAddCustomContactData) => {
    console.log(row)
    editDialogFlag.value = true
    editIndex.value = row.index as number
    addCustomContactData.value = JSON.parse(JSON.stringify(row))
    addCustomContactFlag.value = true
}

const delCustomContact = (idx: number) => {
    ElMessageBox.confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            crmDetail.value.supplementContacts?.splice(idx, 1)
            crmService
                .crmUpdate({ supplementContacts: crmDetail.value.supplementContacts, leadId: crmDetail.value.id })
                .then(() => {
                    ElMessage({ type: 'success', message: '删除成功' })
                    pageInfo.value.total -= 1
                })
        })
        .catch(() => {})
}

const contactsData = computed(() => {
    if (!crmDetail.value.supplementContacts?.length) {
        return []
    }
    return crmDetail.value.supplementContacts
        .slice((pageInfo.value.page - 1) * pageInfo.value.pageSize, pageInfo.value.page * pageInfo.value.pageSize)
        .map((item, idx) => {
            return { ...item, index: (pageInfo.value.page - 1) * pageInfo.value.pageSize + idx }
        })
})

onMounted(() => {})
</script>

<style lang="scss" scoped></style>
