<script lang="ts" setup>
import { ref } from 'vue'
import { parseTime } from '@/utils/parse-time'
import crmService from '@/service/crmService'
import type { ILeadData, ICrmGetActiviesItem } from '@/types/lead'
const props = defineProps<{
    rowItem: ILeadData
}>()
const loading = ref(false)
const activitiesList = ref<ICrmGetActiviesItem[]>([])
const getSalesDynamicsList = () => {
    loading.value = true
    let obj = {
        leadId: props.rowItem.id,
        activityType: '',
        page: 1,
        pageSize: 100,
    }
    crmService.crmGetActivities(obj).then((res) => {
        activitiesList.value = res.data
        loading.value = false
    })
}

const showSalesActivies = () => {
    // showFlag.value= !showFlag.value
    getSalesDynamicsList()
}
const getFollowTypeStr = (type: string | undefined) => {
    switch (type) {
    case 'toUser':
        return '转移'
    case 'update':
        return '跟进状态'
    case 'create':
        return '跟进'
    case 'turn':
        return '新增'
    case 'outbound':
        return '智能外呼'
    case 'other':
        return '其他'
    }
}

const getFileUrl = (name: string) => {
    const Url = `https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/`
    // console.log('Url1111', `${Url}${name}`)
    return `${Url}${name}`
}

const downloadFile = (name: string) => {
    const url = getFileUrl(name)
    window.location.href = url
}
</script>
<template>
    <el-popover v-if="rowItem.newFollowDescription" placement="bottom" :width="500" v-loading="!loading" trigger="hover" @show="showSalesActivies">
        <template #reference>
            <div
                style="
                    color: var(--main-blue-);
                    cursor: pointer;
                    width: 160px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                "
            >
                <span>{{ rowItem.newFollowDescription }}</span>
                <span class="main-color pointer" v-if="rowItem.newFollowImg?.length">[图片]</span>
                <span class="main-color pointer" v-if="rowItem.newFollowFiles?.length">{{
                    rowItem?.newFollowFiles[0].name
                }}</span>
            </div>
        </template>
        <div class="activesBox">
            <div
                v-for="(item, idx) in activitiesList"
                :key="idx"
                class="tb-padding-12 lr-padding-16"
                :class="idx < activitiesList.length - 1 ? 'border-bottom' : ''"
            >
                <div class="display-flex space-between">
                    <div class="display-flex top-bottom-center gap-10">
                        <span><el-tag type="primary">{{ getFollowTypeStr(item.activityType) }}</el-tag></span>
                        <span class="font-bold font-16 l-margin-10">{{ item.username || '-' }}</span>
                        <span class="l-margin-6">跟进方式:{{ item.followType || '-' }}</span>
                    </div>
                    <div>
                        {{ parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                    </div>
                </div>
                <!-- <div class="t-margin-6" v-if="item.activityType === 'turn'">
                    <view class="display-flex font-14 t-margin-8">
                        <view class="color-black">
                            {{ item.description }}
                        </view>
                    </view>
                </div> -->
                <div class="t-margin-6">{{ item.description }}</div>
                <div v-if="item.followImg?.length" class="t-margin-6 border-radius-6 font-14">
                    <el-image
                        v-for="(img, i) in item.followImg"
                        :key="img"
                        :preview-src-list="
                            item.followImg.map((im) => {
                                return getFileUrl(im.name)
                            })
                        "
                        style="width: 50px; height: 50px"
                        class="l-margin-6"
                        :initial-index="i"
                        :src="getFileUrl(img.name)"
                        :zoom-rate="2"
                        fit="cover"
                        :preview-teleported="true"
                    />
                </div>
                <div v-if="item.followFiles?.length">
                    <div class="t-margin-6 border-radius-6 font-14" v-for="(file, i) in item.followFiles" :key="i">
                        <text>{{ file.originalName }}</text>
                        <text class="l-margin-10 font-12 !color-blue pointer" @click="downloadFile(file.name)">下载</text>
                    </div>
                </div>
            </div>
            <div v-if="!activitiesList.length && loading">
                <el-empty :image-size="50" description="暂无跟进记录" />
            </div>
        </div>
    </el-popover>
    <div v-else>-</div>
</template>
<style scoped lang="scss">
.activesBox {
    max-height: 200px;
    overflow-y: auto;
}
</style>
