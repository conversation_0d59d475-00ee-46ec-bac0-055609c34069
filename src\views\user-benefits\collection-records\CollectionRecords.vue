<template>
    <div style="background-color: #f7f7f7; ">
        <div style=" background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                :searchOptionKey="searchOptionKey"
                @updateSearchParams="updateSearchParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="display-flex top-bottom-center">
                <div class="r-margin-10 color-three-grey font-14">已选{{ selectedLength }}</div>
                <div class="l-margin-10 font-16" v-if="permissionService.isCollectionRecordsExportPermitted()">
                    <el-dropdown>
                        <el-button>
                            导出
                            <el-icon class="el-icon--right">
                                <CaretBottom />
                            </el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="handleExport(selectedLength, 1)">导出所选</el-dropdown-item>
                                <el-dropdown-item @click="handleExport(100, 0)">导出前100条</el-dropdown-item>
                                <el-dropdown-item @click="handleExport(500, 0)">导出前500条</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <div class="t-margin-20">
                <el-table
                    ref="tableList"
                    :data="CollectLogData"
                    style="width: 100%;height: 500px;"
                    class="el-table"
                    v-loading="tableLoading"
                    @selection-change="handleSelectionChange"
                    show-overflow-tooltip
                    row-key="id"
                    empty-text="暂无数据"
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column type="selection" width="55" reserve-selection fixed="left" />
                    <el-table-column v-if="userRole === 'admin' || 'yuanqu_admin'" prop="orgName" label="组织名称" min-width="150"> </el-table-column>
                    <el-table-column v-if="userRole === 'admin'" prop="tenantName" label="所属租户" min-width="150"> </el-table-column>
                    <el-table-column prop="nickname" label="发起人" min-width="100"> </el-table-column>
                    <el-table-column prop="executionTime" label="执行时间" min-width="170">
                        <template #default="scope">
                            <div>
                                {{
                                    scope.row.executionTime
                                        ? moment(scope.row.executionTime).format('YYYY-MM-DD HH:mm:ss')
                                        : '-'
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="collectType" label="采集类型" min-width="100">
                        <template #default="scope">
                            <span v-if="scope.row.collectType === 'RISK'">风险采集</span>
                            <span v-else-if="scope.row.collectType === 'INVOICE'">发票采集</span>
                            <span v-else-if="scope.row.collectType === 'TAX'">财税采集</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="采集状态" width="100">
                        <template #default="scope">
                            <span v-if="scope.row.status === 'SUCCESS'">采集成功</span>
                            <span v-else-if="scope.row.status === 'WAITING'">等待采集</span>
                            <span v-else-if="scope.row.status === 'ERROR'">采集失败</span>
                            <span v-else-if="scope.row.status === 'COLLECTING'">采集中</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="companyName" label="企业名称" min-width="200"> </el-table-column>
                    <el-table-column prop="socialCreditCode" label="企业税号" min-width="200"> </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" min-width="170">
                        <template #default="scope">
                            <div>
                                {{
                                    scope.row.createTime
                                        ? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
                                        : '-'
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="96px">
                        <template #default="scope">
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                v-if="scope.row.status === 'SUCCESS'"
                                type="primary"
                                @click="downLoadReport(scope.row)"
                            >
                                下载报告
                            </div>
                        </template>
                    </el-table-column>
                </el-table> 
                <!-- 分页器 -->
                <el-affix position="bottom">
                    <div class="pagination-bar">
                        <el-pagination
                            v-model:currentPage="pageInfo.page"
                            v-model:page-size="pageInfo.pageSize"
                            :total="pageInfo.total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @change="pageChange"
                        />
                    </div>
                </el-affix>    
            </div>
        </div>
        <el-dialog 
            title="导出" 
            :exportdata="exportData" 
            :filename="fileName" 
            v-model="showExportDialog" 
            width="500"
        >
            <div class="lr-padding-16" v-loading="exportLoading">
                <div class="color-three-grey font-14 b-margin-16">注:导出过程请不要关闭当前窗口或刷新页面</div>
                <div  class="display-flex" style="justify-content: space-between">
                    <span class="font-16">{{ fileName }}</span>
                    <a v-if="exportData" class="pointer font-16 color-blue" @click="Export(exportData, fileName)">
                        点击下载
                    </a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import type { RootState } from '@/types/store'
import type { SearchCollectLogItem, CollectLogParams, ExportParams, DownlodaReportParams } from '@/types/report'
import { getCurrentInstance, ref, computed, onMounted, reactive, onBeforeMount } from 'vue'
import { useStore } from 'vuex'
import { ElMessage,ElLoading,ElMessageBox } from 'element-plus'
import searchBox from '@/components/common/SearchBox.vue'
import reportService from '@/service/reportService'
import orderService from '@/service/orderService'
import systemService from '@/service/systemService'
import permissionService from '@/service/permissionService'
// import type { IGetOrgNames } from '@/types/user'
// import type { ITenantListResponse } from '@/types/tenant'

// const orgRes = ref<IGetOrgNames[]>([])
// const tenantRes = ref<ITenantListResponse[]>([])
type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const tableList = ref()
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const searchOptionKey = ref('')
const tableLoading = ref(false)
const CollectLogData = ref<SearchCollectLogItem[]>([])
const selectedData = ref<SearchCollectLogItem[]>([])
const checkIds = ref<string[]>([])
const showExportDialog = ref(false)
const exportLoading = ref(false)
// 根据userRole判断是否显示组织名称和所属租户
const userRole = ref('')
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})
const downloadparams = ref<DownlodaReportParams>({})
const fileName = ref('')
const exportData = ref<Blob>()
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})

const collectLogParams = ref<CollectLogParams>({
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
})

const collectExportParams = ref<ExportParams>({
    collectType: null,
    companyName: '',
    createUser: '',
    ids: [],
    nums: 0,
    orgId: '',
    socialCreditCode: '',
    status: null,
    tenantId: '',
})


const pageChange = (currentPage: number, currentPagesize: number) => {
    collectLogParams.value.page = currentPage
    collectLogParams.value.pageSize = currentPagesize
    getCollectInfo(collectLogParams.value)
}

const updateSearchParams = (param: CollectLogParams) =>{
    collectLogParams.value = param
    getCollectInfo(collectLogParams.value)
}

const getCollectInfo = (Params: CollectLogParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    // console.log('collectLogParams222', Params)
    reportService.collectPage(Params).then(response => {
        CollectLogData.value = response.data
        totalNum.value = response.total
    }).finally(() => {
        tableLoading.value = false 
    })
}

const handleExport = (num: number, type: number) => {
    if (num > 0) {
        showExportDialog.value = true
        exportLoading.value = true
        if (type === 1) {
            collectExportParams.value.ids = checkIds.value
            collectExportParams.value.nums = num
        } else {
            collectExportParams.value.ids = []
            collectExportParams.value.nums = num
        }
        reportService
            .collectExport(collectExportParams.value)
            .then((response) => {
                exportData.value = response
                fileName.value = `${Date.now()}_${user.value?.nickname}_采集记录导出.xlsx`
                // console.log('response', response)
                // console.log('exportData', exportData)
                // console.log('导出', num, '条数据')
                exportLoading.value = false
            })
            .catch((error) => {
                exportLoading.value = false
                ElMessage.error(error)
                // console.error('导出失败:', error)
            })
    } else {
        ElMessage.warning('请选择需要导出的企业')
    }
}

const handleSelectionChange = (val: SearchCollectLogItem[]) => {
    if (val && val.length < 1001) {
        selectedData.value = val
        checkIds.value = val.map((i) => {
            return i.id
        })
    } else {
        ElMessage.warning('最多只能选择1000条数据')
    }
}

const selectedLength = computed(() => selectedData.value.length)

// 导出文件
const Export = (response: Blob, fileName: string) => {
    // console.log('下载', response)
    const blod = new Blob([response], { type: 'application/vnd.ms-excel' })
    // console.log('blod', blod)
    const url = window.URL.createObjectURL(blod)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    // 清除勾选状态
    selectedData.value = []
    checkIds.value = []
    tableList.value.clearSelection()
    showExportDialog.value = false
    getCollectInfo(collectLogParams.value)
}

const downLoadReport = (row: SearchCollectLogItem) => {
    const loading = ElLoading.service({
        text: '正在加载，请稍后...',
    })

    downloadparams.value = {
        deductType: row.collectType === 'INVOICE' ? 'fpbg' : 'swbg',
        socialCreditCode: row.socialCreditCode,
        ...(row.collectType === 'INVOICE' ? { requestId: row.requestId } : { taxRequestId: row.requestId }),
    }
    reportService
        .collectGetReportUrl(downloadparams.value)
        .then((response) => {
            console.log('response', response)
            // 要做判断，如果没有购买要先购买,后需要增加逻辑
            if(response.success){
                if(!response.data.isBuy){
                    ElMessageBox.confirm(`该报告还未购买，请购买后下载查看`,'提示',{
                        confirmButtonText: '去购买',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 购买权益
                        orderService.orderBuyLegal({
                            socialCreditCode: row.socialCreditCode,
                            companyName: row.companyName,
                            serviceKey: row.collectType === 'INVOICE' ? 'fpbg' : 'swbg'
                        }).then(() => {
                            ElMessage.success(`购买成功`)
                            downLoadReport(row)
                        })
                    })
                }
                else if (response.data.isBuy) {
                    window.open(response.data.url)
                }
            }
            else {
                ElMessage.error(`${response.errMsg}`)
            }
        })
        .finally(() => {
            loading.close()
        })
}

onBeforeMount(() => {
    // 根据角色判断是否显示组织名称和所属租户
    // admin和运维，支持查询所有用户的采集记录，条件和列表里展示组织名称和所属租户
    // 园区管理员只支持根据组织查询，条件和列表里只展示组织名称
    // console.log('用户角色', user.value?.role)
    if(user.value?.role.includes('admin') || user.value?.role.includes('yunwei')){
        userRole.value = 'admin'
        searchOptionKey.value = 'COLLECT_SEARCH_OPTIONS_FORADMIN'
    }else if(user.value?.role.includes('yuanqu_admin')){
        userRole.value = 'yuanqu_admin'
        searchOptionKey.value = 'COLLECT_SEARCH_OPTIONS_FORYUANQUADMIN'
    }else{
        searchOptionKey.value = 'COLLECT_SEARCH_OPTIONS'
    }
    // 根据角色判断是否有权限查全部租户
    if (user.value?.role.includes('admin') || user.value?.role.includes('yunwei')) {
        systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }
    // 查询组织，同理
    if (user.value?.role.includes('admin') || user.value?.role.includes('yunwei') || user.value?.role.includes('yuanqu_admin')){
        systemService.orgGetOrgByScopeData('collect').then(response => {
            if(response.data){
                searchConfig.value = {
                    ...searchConfig.value,
                    orgIds:response.data.map(item => ({
                        label:item.name,
                        value:item.id
                    }))
                }
            }
        })
    }
})

onMounted(() => {
    systemService.userGetUserByScopeData('collect').then(response => {
        searchConfig.value = {
            ...searchConfig.value,
            createUser:response.data.map(item => ({ 
                value: item.id,
                label: item.nickname 
            }))
        }
    })

    getCollectInfo(collectLogParams.value)
})

</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}

</style>
