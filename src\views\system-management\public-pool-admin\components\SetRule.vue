<template>
    <div>
        <el-dialog 
            title="规则设置" 
            width="650px" 
            v-model="dialogVisible"
            @close="handleClose"
        >
            <div>
                <!-- 规则设置内容 -->
                <div class="display-flex top-bottom-center b-margin-10">
                    <div class="r-margin-20 font-16 color-black">是否开启自动回收</div>
                    <el-switch
                        v-model="isRecyclevalue"
                        active-color="#13ce66"
                        inactive-color="#ff4949">
                    </el-switch>
                </div >
                <div v-if="isRecyclevalue">
                    <el-form-item label="回收规则" :label-position="'left'">
                        <view class="l-margin-20">
                            <view v-for="(rule, index) in form.reback_rules" :key="index">
                                <view class="display-flex top-bottom-center gap-12" :class=" index == 0 ? '' : 't-margin-6'">
                                    <el-input-number v-model="rule.dayNum" :min="2" :max="365" label="时间" :step="1" style="width: 200px"></el-input-number>天
                                    <!-- 根据长度控制选择的规则类型 -->
                                    <el-select v-if="form.reback_rules.length === 1" v-model="rule.type" placeholder="请选择">
                                        <el-option label="未产生跟进行为" value="1"></el-option>
                                        <el-option label="未转成交客户" value="2"></el-option>
                                    </el-select>
                                    <el-select v-if="form.reback_rules.length === 2" v-model="rule.type" placeholder="请选择">
                                        <el-option v-if="rule.type !== '2'" label="未产生跟进行为" value="1"></el-option>
                                        <el-option v-if="rule.type !== '1'" label="未转成交客户" value="2"></el-option>
                                    </el-select>
                                    <el-icon v-if="form.reback_rules.length>1" @click="delRules(index)" ><Remove /></el-icon>
                                    <el-icon v-if="form.reback_rules.length<2" @click="addRules(form.reback_rules[0])"><CirclePlus /></el-icon>
                                </view>
                                <view v-if="index !== form.reback_rules.length-1" class="relative">
                                    <span class="absolute" style="left: -20px; top: -14px;">或</span></view>
                            </view>
                        </view>
                    </el-form-item>
                    <el-form-item label="回收规则优先级" :label-position="'left'" style="margin-bottom: 0px;">
                        <el-input-number v-model="form.sort" :min="1" :max="99" label="回收规则优先级" :step="1"></el-input-number>
                    </el-form-item>

                </div>
                
                <el-form-item class="font-16 t-margin-20" :label-position="'left'" v-if="poolname === 'customerpool'" >
                    <div class="r-margin-20 font-16 color-black">抢回规则</div>
                    <el-switch 
                        v-model="isGetRebackValue" 
                        active-color="#13ce66"
                        inactive-color="#ff4949">
                    </el-switch>
                </el-form-item>

                <view v-if="isGetRebackValue">
                    <view class="display-flex justify-flex-end b-margin-12 top-bottom-center" style="width: 100%;">
                        <el-button plain @click="addEmployee">新增特殊员工</el-button>
                    </view>
                    <el-table :data="form.getback_rules" style="width: 100%">
                        <el-table-column label="每天可抢公海客户数为" width="200">
                            <template #default="scope">
                                <el-input-number style="width: 150px" v-model="scope.row.num" :step="1" :min="1">
                                    <template #suffix>
                                        <span>条</span>
                                    </template>
                                </el-input-number>
                            </template>
                        </el-table-column>

                        <el-table-column label="规则针对人员" width="300">
                            <template #default="scope">
                                <el-select
                                    v-if="!scope.row.allUser"
                                    v-model="scope.row.users"
                                    filterable
                                    multiple
                                    collapse-tags
                                    collapse-tags-tooltip
                                    placeholder="针对人员"
                                    :max-collapse-tags="10"
                                >
                                    <el-option 
                                        :disabled="getUseDis(item.id)" 
                                        v-for="(item,index) in props.setRuleData.users" 
                                        :key="index" 
                                        :label="item.nickname" 
                                        :value="item.id">
                                        <!-- <el-tag>{{ item.nickname }}</el-tag> -->
                                    </el-option>
                                </el-select>
                                <view v-else>所有公海成员（特殊员工除外）</view>
                            </template>
                        </el-table-column>
                        <el-table-column label="" min-width="80" align="right">
                            <template #default="scope">
                                <el-icon class="pointer" color="#409efc" @click="delRule(scope.$index)" v-if="!scope.row.allUser">
                                    <Delete />
                                </el-icon>
                            </template>				
                        </el-table-column>
                    </el-table>
                </view>

                <div class="display-flex justify-flex-end all-padding-10 t-margin-20">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="confirm" :loading="loading" style="background-color: #1966FF;">确 定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted} from 'vue'
import type { SearchPoolListResponseItem,SetRuleParams, ReBackRules, GetrebackRule } from '@/types/lead'
import crmService from '@/service/crmService'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { ElMessage } from 'element-plus'

const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})

const loading = ref(false)
const updateParams = ref<SetRuleParams>({
    poolId: '',
    type: 2,
})

const props = defineProps<{
    visible: boolean
    setRuleData:SearchPoolListResponseItem
    poolname: string
}>()

const allUsers = props.setRuleData?.users || []

const isRecyclevalue = ref(false)
const isGetRebackValue = ref(false)
const form = ref<{
    sort: number;
    reback_rules: ReBackRules[];
    getback_rules: GetrebackRule[];
}>({
    sort:1 ,
    reback_rules: [
        {
            dayNum: 2,
            type: '1'
        }
    ], 
    getback_rules:[
        {
            num: 100,
            users: [],
            allUser: true
        }
    ]
})

const emit = defineEmits(['update:visible','refreshData'])

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const handleClose = () => {
    dialogVisible.value = false
}

const cancel = () => {
    dialogVisible.value = false
}

const addRules = (row:ReBackRules) => {
    console.log('addRules', row)
    if(row.type === '1'){
        form.value.reback_rules.push({
            dayNum: 2,
            type: '2'
        })
    }else if(row.type === '2'){
        form.value.reback_rules.push({
            dayNum: 2,
            type: '1'
        })
    }
}

const delRules = (index: number) => {
    if (form.value.reback_rules.length > 1) {
        form.value.reback_rules.splice(index, 1)
    }
}

const confirm = () => {
    loading.value = true
    updateParams.value.poolId = props.setRuleData.id 
    if( !isRecyclevalue.value ){
        updateParams.value.openReback = 0
        updateParams.value.rebackRules = []
    }
    if(!isGetRebackValue.value){
        updateParams.value.openGetrebackRule = 0
        updateParams.value.getbackRules = []
    }
    if(isRecyclevalue.value && props.setRuleData){
        const reBackRules = []
        updateParams.value.sort = form.value.sort
        updateParams.value.openReback = 1
        for(let i = 0; i < form.value.reback_rules.length;i++){
            reBackRules.push({
                dayNum: form.value.reback_rules[i].dayNum,
                type: form.value.reback_rules[i].type
            })
        }
        updateParams.value.rebackRules = reBackRules
    }
    if(isGetRebackValue.value && props.setRuleData){
        const getbackRules = []
        getbackRules.push({allUser: true, num: 100,users: []})
        updateParams.value.openGetrebackRule = 1
        for(let i = 1; i < form.value.getback_rules.length;i++){
            getbackRules.push({
                allUser:false,
                num:form.value.getback_rules[i].num,
                users: form.value.getback_rules[i].users,
            })
        }
        updateParams.value.getbackRules = getbackRules
    }
    crmService.crmManageUpdate(updateParams.value)
        .then((res) => {
            if(res.success){
                ElMessage.success('规则设置成功')
            }else{
                ElMessage.error(`${res.errMsg}`)
            }
        })
        .finally(() => {
            loading.value = false
            updateParams.value = {
                poolId: '',
                type: 2,
                sort: 1,
            }
            dialogVisible.value = false
            emit('refreshData')
        })
}

const addEmployee = () => {
    console.log('新增特殊员工',props.setRuleData)
    form.value.getback_rules.push({
        num: 1,
        users: [],
        allUser: false
    })
}

const getUseDis = (id: string) => {
    return form.value.getback_rules.some(rule => rule.users.includes(id))
}

const delRule = (index: number) => {
    form.value.getback_rules.splice(index, 1)
}

watch(
    () => props.setRuleData,
    (newVal) => {
        console.log('setRuleData changed to:', newVal)
        if (newVal) {
            if(newVal.openReback === 1){
                isRecyclevalue.value = true
                form.value.reback_rules = newVal.rebackRules || []
                form.value.sort = newVal.sort || 1
            }else{
                isRecyclevalue.value = false
            }  
            if(newVal.openGetrebackRule === 1 && newVal.getbackRules){
                isGetRebackValue.value = true
                form.value.getback_rules = newVal.getbackRules.map(rule => ({
                    num: rule.num,
                    users: rule.users ? [...rule.users] : [],
                    allUser: rule.allUser
                }))
                
            }else{
                console.log('setRuleData changed to222222:')
                isGetRebackValue.value = false
            }
        }
        console.log('form', form.value)
    },
    { immediate: true }
)

onMounted(() => {
    console.log('allUsers', allUsers)
    console.log('123123',user.value)
})

</script>

<style scoped>

</style>
