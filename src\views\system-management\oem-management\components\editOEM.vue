<template>
    <div class="all-paddding-16">
        <div>
            <div class="border"></div>
            <div class="flex top-bottom-center gap-8 t-margin-16">
                <span class="font-24 flex top-bottom-center" style="color: #D66353;">*</span>
                <span class="color-two-grey font-16">oem_key</span>
                <el-input
                    v-model="oem_key"
                    placeholder="请输入渠道名"
                    style="width: 20%;"
                ></el-input>
            </div>
        </div>
        <el-tabs v-model="activeName" class="demo-tabs t-margin-16" >
            <el-tab-pane label="臻企云·数字化产业融合协同平台" name="xtpt">
                <div class="flex top-bottom-center gap-8 b-margin-16">
                    <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
                    <span class="color-black font-16">登录页</span>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>域名</span>
                        <el-input
                            v-model="yuming"
                            placeholder="请输入域名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>底部时间(默认值为2016-2025)</span>
                        <el-date-picker
                            v-model="yearrange"
                            type="yearrange"
                            range-separator="To"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            style="width: 100%;"  
                        />
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>底部公司名(默认值为数组科技（南京）股份有限公司)</span>
                        <el-input
                            v-model="gongsiming"
                            placeholder="请输入公司名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>左侧顶部文字(默认值为臻企云·数字化产业融合协同平台)</span>
                        <el-input
                            v-model="dingbuwenzi"
                            placeholder="请输入顶部文字"
                        ></el-input>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>左上角logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleAvatarProgress"
                            :on-success="handleAvatarSuccess"
                        >
                            <img v-if="imageUrl && ！" :src="imageUrl" class="avatar" />
                            <el-icon v-else-if="imageLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>登录页图片</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleAvatarProgress"
                            :on-success="handleAvatarSuccess"
                        >
                            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                            <el-icon v-else-if="imageLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                            
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>网页页签标题(默认值为臻企云·数字化产业融合协同平台)</span>
                        <el-input
                            v-model="yeqianbiaoti"
                            placeholder="请输入网页页签标题"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>底部的备案号</span>
                        <el-input
                            v-model="beianhao"
                            placeholder="请输入备案号"
                        ></el-input>
                    </div>
                </div>
                <div class="flex top-bottom-center gap-8 tb-margin-16">
                    <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
                    <span class="color-black font-16">首页</span>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页上方的logo</span>
                        <el-input
                            v-model="yuming"
                            placeholder="请输入域名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>帮助手册地址</span>
                        <el-input
                            v-model="bangzhushouce"
                            placeholder="请输入帮助手册地址"
                        ></el-input>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="经营慧眼" name="jyhy">Config</el-tab-pane>
            <el-tab-pane label="报告" name="bg">Role</el-tab-pane>
            <el-tab-pane label="其他" name="other">Task</el-tab-pane>
        </el-tabs>
        <div class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="save()" :loading="loading" >
                确定
            </el-button>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref } from 'vue'
import type { UploadProps } from 'element-plus'

const loading = ref(false)

const activeName = ref('xtpt')

// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}

// 表单参数
const oem_key = ref('')
const yuming = ref('')
const yearrange = ref('')
const gongsiming = ref('')
const dingbuwenzi = ref('')
const yeqianbiaoti = ref('')
const beianhao = ref('')
const bangzhushouce = ref('')


const imageUrl = ref('')
const imageLoading = ref(false)
const handleAvatarProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    imageLoading.value = true
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
    response,
    uploadFile
) => {
    imageLoading.value = false
    imageUrl.value = URL.createObjectURL(uploadFile.raw!)
}


const emit = defineEmits(['cancel'])
const handleClose = () => {
    emit('cancel')
}
const save = () => {

}

</script>

<style lang='scss' scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}


.dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    margin-top: 20px;
}


:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

</style>