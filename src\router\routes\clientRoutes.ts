import { type RouteRecordRaw } from 'vue-router'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import Home from '@/views/home/<USER>'
import Search from '@/views/find-companies/search/Search.vue'
import BasicSearch from '@/views/find-companies/search/basic-search/BasicSearch.vue'
import MapSearch from '@/views/find-companies/map-search/MapSearch.vue'
import AdvanceSearch from '@/views/find-companies/advance-search/AdvanceSearch.vue'
import CompanyProfile from '@/views/find-companies/company-profile/CompanyProfile.vue'
import LeadPool from '@/views/leads/lead-pool/LeadPool.vue'
import LeadList from '@/views/leads/lead-list/LeadList.vue'
import CustomerPublicPool from '@/views/leads/customer-public-pool/CustomerPublicPool.vue'
import CustomerList from '@/views/leads/customer-list/CustomerList.vue'
import DialingTasks from '@/views/auto-dialer/dialing-tasks/DialingTasks.vue'
import DialingContacts from '@/views/auto-dialer/dialing-contacts/DialingContacts.vue'
import SMSSettings from '@/views/operations/sms-settings/SMSSettings.vue'
import PolicyManagement from '@/views/operations/policy-management/PolicyManagement.vue'
import FinancialProducts from '@/views/operations/financial-products/FinancialProducts.vue'
import RiskMonitoring from '@/views/risk-management/risk-monitoring/RiskMonitoring.vue'
import RiskAlerts from '@/views/risk-management/risk-alerts/RiskAlerts.vue'
import BenefitList from '@/views/user-benefits/benefit-list/BenefitList.vue'
import CollectionRecords from '@/views/user-benefits/collection-records/CollectionRecords.vue'
import InternalOrganization from '@/views/system-management/internal-organization/InternalOrganization.vue'
import CRMTagging from '@/views/system-management/crm-tagging/CRMTagging.vue'
import BusinessSettings from '@/views/system-management/business-settings/BusinessSettings.vue'
import LeadPoolAdmin from '@/views/system-management/lead-pool-admin/LeadPoolAdmin.vue'
import PublicPoolAdmin from '@/views/system-management/public-pool-admin/PublicPoolAdmin.vue'
import TenantManagement from '@/views/system-management/tenant-management/TenantManagement.vue'
import DataChannel from '@/views/system-management/data-channel/DataChannel.vue'
import ProjectClassificationDetail from '@/views/operations/project-classification/EditProjectClassification.vue'
import ProjectAdvanceTemplate from '@/views/operations/project-advance-template/ProjectAdvanceTemplate.vue'
import ProjectClassification from '@/views/operations/project-classification/ProjectClassification.vue'
import BidSearch from '@/views/find-companies/search/bid-search/BidSearch.vue'
import FactorySearch from '@/views/find-companies/search/factory-search/FactorySearch.vue'
import DialingTaskDetail from '@/views/auto-dialer/dialing-task-detail/DialingTaskDetail.vue'
import MenuManagement from '@/views/system-management/menu-management/MenuManagement.vue'
import TaskManagement from '@/views/system-management/task-management/TaskManagement.vue'
import RecentRegSearch from '@/views/find-companies/search/recent-reg-search/RecentRegSearch.vue'
import BenefitManagement from '@/views/user-benefits/benefit-management/BenefitManagement.vue'

const clientRoutes: Array<RouteRecordRaw> = [
    {
        path: '/',
        component: DefaultLayout,
        redirect: '/home',
        meta: { rely: true },
        children: [
            {
                path: '/home',
                name: 'home',
                component: Home,
                meta: { requiresAuth: true, title: '首页' },
            },
        ],
    },
    {
        path: '/find-companies',
        name: 'search',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '查找企业' },
        redirect: '/find-companies/search',
        children: [
            {
                path: 'search',
                name: 'search-company',
                component: Search,
                meta: { requiresAuth: true, title: '找企业', fullContainer: true },
            },
            {
                path: 'search/basic/:tagTypeVal?/:keyword?',
                name: 'basic-search',
                component: BasicSearch,
                meta: { requiresAuth: true, title: '智能搜索', rely: true, defaultActive: 'search-company' },
            },
            {
                path: 'search/advance/:templeteId?/:type?',
                name: 'more-search-company',
                component: AdvanceSearch,
                meta: { requiresAuth: true, title: '高级搜索' },
            },
            {
                path: 'search/map',
                name: 'company-map',
                component: MapSearch,
                meta: { requiresAuth: true, title: '地图搜索', fullContainer: true, hideRouterBread: true },
            },
            {
                path: 'search/bid',
                name: 'bid-search',
                component: BidSearch,
                meta: { requiresAuth: true, title: '搜招标投标', rely: true, defaultActive: 'search-company' },
            },
            {
                path: 'search/factory',
                name: 'factory-search',
                component: FactorySearch,
                meta: { requiresAuth: true, title: '搜工厂', rely: true, defaultActive: 'search-company' },
            },
            {
                path: 'search/recent',
                name: 'recent-search',
                component: RecentRegSearch,
                meta: { requiresAuth: true, title: '最新注册', rely: true, defaultActive: 'search-company' },
            },
        ],
    },
    {
        path: '/find-companies',
        component: DefaultLayout,
        redirect: '/company-profile',
        meta: { rely: true },
        children: [
            {
                path: '/company-profile/:socialCreditCode',
                name: 'company-profile',
                component: CompanyProfile,
                meta: { requiresAuth: true, title: '企业详情', rely: true },
            },
        ],
    },
    {
        path: '/leads',
        redirect: 'lead-pool',
        name: 'su-crm',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '客户线索' },
        children: [
            {
                path: 'lead-pool/',
                name: 'su-crm-lead-pool',
                component: LeadPool,
                meta: { requiresAuth: true, title: '线索池' },
            },
            {
                path: 'lead-list',
                name: 'su-crm-lead-list',
                component: LeadList,
                meta: { requiresAuth: true, title: '线索列表' },
            },
            {
                path: 'customer-public-pool',
                name: 'crm-customer-poll',
                component: CustomerPublicPool,
                meta: { requiresAuth: true, title: '客户公海' },
            },
            {
                path: 'customer-list',
                name: 'su-crm-customer-list',
                component: CustomerList,
                meta: { requiresAuth: true, title: '客户列表' },
            },
        ],
    },
    {
        path: '/risk-management',
        name: 'company-manage',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '风险管理' },
        children: [
            {
                path: 'risk-monitoring',
                name: 'risk-monitor',
                component: RiskMonitoring,
                meta: { requiresAuth: true, title: '风险监控' },
            },
            {
                path: 'risk-alerts',
                name: 'risk-manage',
                component: RiskAlerts,
                meta: { requiresAuth: true, title: '风险列表' },
            },
        ],
    },
    {
        path: '/data-center',
        name: 'data-center',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '数据中心' },
        children: [],
    },
    {
        path: '/auto-dialer',
        name: 'ai-phone',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '智能外呼' },
        children: [
            {
                path: 'dialing-task',
                name: 'ai-phone-task',
                component: DialingTasks,
                meta: { requiresAuth: true, title: '外呼任务' },
            },
            {
                path: 'dialing-contacts',
                name: 'ai-phone-customer',
                component: DialingContacts,
                meta: { requiresAuth: true, title: '外呼客户' },
            },
            {
                path: 'dialing-task/detail',
                name: 'dialing-task-detail',
                component: DialingTaskDetail,
                meta: { requiresAuth: true, title: '任务详情', rely: true, defaultActive: 'ai-phone-task' },
            },
        ],
    },
    {
        path: '/operations',
        name: 'operate-manage',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '运营管理' },
        children: [
            {
                path: 'sms-settings',
                name: 'sms-settings',
                component: SMSSettings,
                meta: { requiresAuth: true, title: '短信配置' },
            },
            {
                path: 'policy-management',
                name: 'policy-management',
                component: PolicyManagement,
                meta: { requiresAuth: true, title: '政策管理' },
            },
            {
                path: 'financial-products',
                name: 'financial-products',
                component: FinancialProducts,
                meta: { requiresAuth: true, title: '金融产品管理' },
            },
            {
                path: 'project-classification',
                name: 'project-classification',
                component: ProjectClassification,
                meta: { requiresAuth: true, title: '项目分类' },
            },
            {
                path: 'project-classification-detail/:id',
                name: 'project-classification-detail',
                component: ProjectClassificationDetail,
                meta: { requiresAuth: true, title: '编辑图谱', rely: true },
            },
            {
                path: 'project-advance-template',
                name: 'advance-template',
                component: ProjectAdvanceTemplate,
                meta: { requiresAuth: true, title: '高级搜索模板', rely: true },
            },
        ],
    },
    {
        path: '/user-benefits',
        name: 'user-overview',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '权益中心' },
        children: [
            {
                path: 'benefit-list',
                name: 'benefit-list',
                component: BenefitList,
                meta: { requiresAuth: true, title: '权益列表' },
            },
            {
                path: 'collection-records',
                name: 'collection-records',
                component: CollectionRecords,
                meta: { requiresAuth: true, title: '采集记录' },
            },
            {
                path: 'benefit-management',
                name: 'benefit-management',
                component: BenefitManagement,
                meta: { requiresAuth: true, title: '权益管理' },
            },
        ],
    },
    {
        path: '/system-management',
        redirect: { name: 'internal-organization' },
        name: 'system-management',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '系统管理' },
        children: [
            {
                path: 'internal-organization/:type?/:name?',
                name: 'mail-list',
                component: InternalOrganization,
                meta: { requiresAuth: true, title: '内部组织管理' },
            },
            {
                path: 'crm-tagging',
                name: 'crm-tag',
                component: CRMTagging,
                meta: { requiresAuth: true, title: '企业标签' },
            },
            {
                path: 'business-settings',
                name: 'tenant-settings',
                component: BusinessSettings,
                meta: { requiresAuth: true, title: '业务配置' },
            },
            {
                path: 'lead-pool-admin',
                name: 'lead-pool-management',
                component: LeadPoolAdmin,
                meta: { requiresAuth: true, title: '线索池管理' },
            },
            {
                path: 'public-pool-admin',
                name: 'customer-pool',
                component: PublicPoolAdmin,
                meta: { requiresAuth: true, title: '客户公海管理' },
            },
            {
                path: 'task-management',
                name: 'task-management',
                component: TaskManagement,
                meta: { requiresAuth: true, title: '任务管理' },
            },
            {
                path: 'tenant-management',
                name: 'tenant-management',
                component: TenantManagement,
                meta: { requiresAuth: true, title: '租户管理' },
            },
            {
                path: 'data-channel',
                name: 'data-channel',
                component: DataChannel,
                meta: { requiresAuth: true, title: '数据通道' },
            },
            {
                path: 'menu-management',
                name: 'menu-management',
                component: MenuManagement,
                meta: { requiresAuth: true, title: '菜单管理' },
            },
        ],
    },
]

export default clientRoutes
